# ListTools 权限系统配置

## 权限节点

### 管理员权限
- **权限节点**: `listtools.admin`
- **描述**: 允许使用所有 ListTools 命令
- **默认**: 仅控制台拥有此权限

## 权限配置示例

### LuckPerms 配置
```bash
# 给予玩家管理员权限
/lp user <玩家名> permission set listtools.admin true

# 给予用户组管理员权限
/lp group <组名> permission set listtools.admin true

# 移除玩家管理员权限
/lp user <玩家名> permission unset listtools.admin
```

### Velocity 权限插件配置
如果您使用其他权限插件，请参考相应插件的文档来配置权限节点。

## 权限检查机制

### 控制台权限
- 控制台（Console）始终拥有所有权限
- 无需额外配置即可使用所有命令

### 玩家权限
- 游戏内玩家必须拥有 `listtools.admin` 权限才能使用命令
- 没有权限的玩家将收到 "你没有权限使用此命令！" 的提示
- Tab补全功能也会根据权限进行过滤

## 支持的命令别名

所有命令别名都使用相同的权限节点：

- `/listtools` - 主命令
- `/lt` - 简写别名
- `/whitelist` - 白名单别名

## 权限保护的功能

拥有 `listtools.admin` 权限的用户可以：

1. **白名单管理**
   - 添加玩家到白名单
   - 从白名单移除玩家
   - 查看白名单列表

2. **系统管理**
   - 重载配置文件
   - 查看插件状态
   - 手动触发白名单检查

3. **Tab补全**
   - 智能命令补全
   - 玩家名称建议
   - 过滤式补全

## 安全建议

1. **谨慎授权**: 只给予可信任的管理员 `listtools.admin` 权限
2. **定期审查**: 定期检查拥有此权限的用户列表
3. **日志监控**: 关注插件日志中的权限相关操作
4. **权限分离**: 考虑为不同级别的管理员创建不同的权限组

## 故障排除

### 常见问题

**Q: 玩家无法使用命令**
A: 检查玩家是否拥有 `listtools.admin` 权限

**Q: Tab补全不工作**
A: 确认玩家有权限，Tab补全会根据权限进行过滤

**Q: 控制台无法使用命令**
A: 控制台应该始终有权限，如果不行请检查插件是否正确加载

### 权限测试

可以使用以下命令测试权限配置：

```bash
# 在控制台执行（应该成功）
listtools status

# 让有权限的玩家执行（应该成功）
/lt status

# 让无权限的玩家执行（应该被拒绝）
/listtools status
```

## 权限系统技术细节

### 实现方式
- 使用 Velocity API 的 `CommandSource.hasPermission()` 方法
- 控制台检查通过 `instanceof Player` 判断
- Tab补全在权限检查失败时返回空列表

### 权限检查时机
- 命令执行前进行权限验证
- Tab补全请求时进行权限过滤
- 每次命令调用都会重新检查权限

### 性能考虑
- 权限检查是轻量级操作
- 不会对服务器性能造成显著影响
- Tab补全会根据权限智能过滤，提升用户体验
